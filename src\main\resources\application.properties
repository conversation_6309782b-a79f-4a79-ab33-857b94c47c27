spring.application.name=todoapp

spring.datasource.url=******************************************
spring.datasource.username=root
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

server.error.whitelabel.enabled=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
